// API Endpoints Configuration
// Tập hợp tất cả các endpoints từ backend routes

const API_URL =
  import.meta.env.VITE_API_URL ||
  "https://project-management-ji01.onrender.com";

// ========== AUTHENTICATION ENDPOINTS ==========
export const AUTH_ENDPOINTS = {
  // Universal login cho tất cả role
  LOGIN: `${API_URL}/api/auth/login`,
  FORGOT_PASSWORD: `${API_URL}/api/auth/forgot-password`,
  RESET_PASSWORD: (token) => `${API_URL}/api/auth/reset-password/${token}`,

  // Profile management
  GET_PROFILE: `${API_URL}/api/auth/profile`,
  GET_USER_PROFILE: (userId) => `${API_URL}/api/admin/profile/${userId}`,
  CHANGE_PASSWORD: `${API_URL}/api/auth/change-password`,
  UPDATE_AVATAR: `${API_URL}/api/admin/update-avatar`,

  // Demo passwords (public)
  DEMO_PASSWORDS: `${API_URL}/api/admin/demo-passwords`,
  
  // Session monitoring
  SESSION_MONITOR: `${API_URL}/api/auth/session-monitor`,
};

// ========== ADMIN ENDPOINTS ==========
export const ADMIN_ENDPOINTS = {
  DASHBOARD: `${API_URL}/api/admin/dashboard`,
  SYSTEM_STATS: `${API_URL}/api/admin/system-stats`,
  USERS: `${API_URL}/api/users`,
  USERS_BY_DEPARTMENT: (departmentId) => `${API_URL}/api/users/department/${departmentId}/members`,
  CREATE_USER: `${API_URL}/api/users`,
  UPDATE_USER: (id) => `${API_URL}/api/users/${id}`,
  DELETE_USER: (id) => `${API_URL}/api/users/${id}`,
  RESTORE_USER: (id) => `${API_URL}/api/users/${id}/restore`,
  TOGGLE_USER_BLOCK: (id) => `${API_URL}/api/users/${id}/toggle-block`,
  CHANGE_USER_ROLE: (id) => `${API_URL}/api/users/${id}/change-role`,
  USER_ACTIVITIES: (id) => `${API_URL}/api/users/${id}/activities`,
  BULK_USER_OPERATIONS: `${API_URL}/api/users//bulk`,
  DELETED_USERS: `${API_URL}/api/users/deleted`,
  PERMANENT_DELETE_USER: (id) => `${API_URL}/api/users/${id}/permanent`,
  PERMANENTLY_DELETED_USERS: `${API_URL}/api/admin/users/permanently-deleted`,
  RESTORE_PERMANENT_USER: (id) =>
    `${API_URL}/api/admin/users/${id}/restore-permanent`,
  // Department
  DEPARTMENTS: `${API_URL}/api/departments`,
  GET_DEPARTMENT: (id) => `${API_URL}/api/departments/${id}`,
  CREATE_DEPARTMENT: `${API_URL}/api/departments`,
  UPDATE_DEPARTMENT: (id) => `${API_URL}/api/departments/${id}`,
  DELETE_DEPARTMENT: (id) => `${API_URL}/api/departments/${id}`,
  // Project
  ALL_PROJECTS: `${API_URL}/api/projects`,
  CREATE_PROJECT: `${API_URL}/api/projects`,
  GET_PROJECT: (id) => `${API_URL}/api/projects/${id}`,
  UPDATE_PROJECT: (id) => `${API_URL}/api/projects/${id}`,
  DELETE_PROJECT: (id) => `${API_URL}/api/projects/${id}`,
  RESTORE_PROJECT: (id) => `${API_URL}/api/projects/${id}/restore`,
  PERMANENT_DELETE_PROJECT: (id) => `${API_URL}/api/projects/${id}/permanent`,
  DELETED_PROJECTS: `${API_URL}/api/projects/deleted`,
  PERMANENTLY_DELETED_PROJECTS: `${API_URL}/api/admin/projects/permanently-deleted`,
  RESTORE_PERMANENT_PROJECT: (id) =>
    `${API_URL}/api/admin/projects/${id}/restore-permanent`,
  // Project Members
  ADD_PROJECT_MEMBER: (id) => `${API_URL}/api/projects/${id}/members`,
  REMOVE_PROJECT_MEMBER: (id, userId) =>
    `${API_URL}/api/projects/${id}/members/${userId}`,
  // Thêm endpoint lấy file của project
  PROJECT_FILES: (projectId) => `${API_URL}/api/projects/${projectId}/files`,
  // Task
  PROJECT_TASKS: (projectId) => `${API_URL}/api/projects/${projectId}/tasks`,
  CREATE_PROJECT_TASK: (projectId) =>
    `${API_URL}/api/projects/${projectId}/tasks`,
  UPDATE_TASK: (projectId, taskId) =>
    `${API_URL}/api/projects/${projectId}/tasks/${taskId}`,
  DELETE_TASK: (projectId, taskId) =>
    `${API_URL}/api/projects/${projectId}/tasks/${taskId}`,
  RESTORE_TASK: (projectId, taskId) =>
    `${API_URL}/api/projects/${projectId}/tasks/${taskId}/restore`,
  RESTORE_PERMANENT_TASK: (taskId) =>
    `${API_URL}/api/admin/tasks/${taskId}/restore-permanent`,
  PERMANENT_DELETE_TASK: (projectId, taskId) =>
    `${API_URL}/api/projects/${projectId}/tasks/${taskId}/permanent`,
  DELETED_TASKS: (projectId) =>
    `${API_URL}/api/projects/${projectId}/tasks/deleted`,
  // My Tasks (Project tasks assigned to current user)
  MY_TASKS: `${API_URL}/api/projects/my-tasks`,
  // Personal Notes
  PERSONAL_TASKS: `${API_URL}/api/common/personal-notes`,
  CREATE_PERSONAL_TASK: `${API_URL}/api/common/personal-notes`,
  UPDATE_PERSONAL_TASK: (taskId) =>
    `${API_URL}/api/common/personal-notes/${taskId}`,
  DELETE_PERSONAL_TASK: (taskId) =>
    `${API_URL}/api/common/personal-notes/${taskId}`,
  // Maintenance
  TOGGLE_MAINTENANCE: `${API_URL}/api/admin/maintenance/toggle`,
  MAINTENANCE_STATUS: `${API_URL}/api/admin/maintenance/status`,
  // Email
  EMAIL_STATUS: `${API_URL}/api/admin/email-status`,
  RETRY_EMAILS: `${API_URL}/api/admin/retry-emails`,
  CLEAN_EMAIL_LOGS: `${API_URL}/api/admin/clean-email-logs`,
  // Activities
  ALL_ACTIVITIES: `${API_URL}/api/admin/activities`,
  PROJECT_MEMBERS: (projectId) =>
    `${API_URL}/api/users/projects/${projectId}/members`,
  
  // Session & IP Management
  GET_USER_SESSION_INFO: (userId) => `${API_URL}/api/users/${userId}/session-info`,
  RESET_IP: (userId) => `${API_URL}/api/users/${userId}/reset-ip`,
  INVALIDATE_USER_SESSION: (userId) => `${API_URL}/api/users/${userId}/invalidate-session`,
};

// ========== CEO ENDPOINTS ==========
export const CEO_ENDPOINTS = ADMIN_ENDPOINTS;

// ========== DEPARTMENT HEAD ENDPOINTS ==========
export const DEPT_HEAD_ENDPOINTS = ADMIN_ENDPOINTS;

// ========== LEADER ENDPOINTS ==========
export const LEADER_ENDPOINTS = ADMIN_ENDPOINTS;

// ========== STAFF ENDPOINTS ==========
export const STAFF_ENDPOINTS = ADMIN_ENDPOINTS;

// ========== HR ENDPOINTS ==========
export const HR_ENDPOINTS = ADMIN_ENDPOINTS;

// ========== SEARCH ENDPOINTS ==========
export const SEARCH_ENDPOINTS = {
  BASIC_SEARCH: (query) =>
    `${API_URL}/api/common/search?query=${encodeURIComponent(query)}`,
  SEARCH_HISTORY: `${API_URL}/api/common/search/history`,
  ADVANCED_SEARCH: `${API_URL}/api/common/search/advanced`,
  DELETE_SEARCH_HISTORY: `${API_URL}/api/common/search/history`,
};

// ========== PERSONAL NOTES ENDPOINTS ==========
export const PERSONAL_NOTES_ENDPOINTS = {
  GET_NOTES: `${API_URL}/api/common/personal-notes`, // GET: lấy tất cả ghi chú
  CREATE_NOTE: `${API_URL}/api/common/personal-notes`, // POST: tạo ghi chú với files
  UPDATE_NOTE: (id) => `${API_URL}/api/common/personal-notes/${id}`,
  DELETE_NOTE: (id) => `${API_URL}/api/common/personal-notes/${id}`,
  
  // File management for personal notes
  UPLOAD_FILES: (id) => `${API_URL}/api/common/personal-notes/${id}/upload`, // POST: upload file
  GET_FILES: (id) => `${API_URL}/api/common/personal-notes/${id}/files`, // GET: lấy danh sách file
  DOWNLOAD_FILE: (id, fileId) => `${API_URL}/api/common/personal-notes/${id}/files/${fileId}/download`, // GET: download file
  DELETE_FILE: (id, fileId) => `${API_URL}/api/common/personal-notes/${id}/files/${fileId}`, // DELETE: xóa file
};

// ========== NOTIFICATION ENDPOINTS ==========
export const NOTIFICATION_ENDPOINTS = {
  ALL: `${API_URL}/api/notification`,
  UNREAD_COUNT: `${API_URL}/api/notification/unread-count`,
  BY_TYPE: (type) => `${API_URL}/api/notification/type/${type}`,
  MARK_READ: (id) => `${API_URL}/api/notification/mark-read/${id}`,
  MARK_ALL_READ: `${API_URL}/api/notification/mark-all-read`,
  MARK_ALL_UNREAD: `${API_URL}/api/notification/mark-all-unread`,
  DELETE_READ_ALL: `${API_URL}/api/notification/delete-read/all`,
  DELETE: (id) => `${API_URL}/api/notification/${id}`,
};

// ========== DOCUMENTS ENDPOINTS ==========
export const DOCUMENTS_ENDPOINTS = {
  DOCUMENTS: `${API_URL}/api/documents`,
  DOCUMENT: (id) => `${API_URL}/api/documents/${id}`,
  CREATE_DOCUMENT: `${API_URL}/api/documents`,
  UPDATE_DOCUMENT: (id) => `${API_URL}/api/documents/${id}`,
  DELETE_DOCUMENT: (id) => `${API_URL}/api/documents/${id}`,
  UPLOAD_DOCUMENT_FILE: (id) => `${API_URL}/api/documents/${id}/upload`,
  DOWNLOAD_DOCUMENT_FILE: (id) => `${API_URL}/api/documents/${id}/download`,
  PROJECT_DOCUMENTS: (projectId) =>
    `${API_URL}/api/projects/${projectId}/documents`,
  PROJECT_FILES: (projectId) => `${API_URL}/api/projects/${projectId}/files`,
  UPLOAD_PROJECT_FILE: (projectId) =>
    `${API_URL}/api/projects/${projectId}/upload`,
  DELETE_PROJECT_FILE: (projectId, fileId) =>
    `${API_URL}/api/projects/${projectId}/files/${fileId}`,
  DOCUMENTS_SEARCH: `${API_URL}/api/documents/search`,
};

// ========== HELPER FUNCTIONS ==========

// Lấy endpoints dựa trên role
export const getEndpointsByRole = (role) => {
  return ADMIN_ENDPOINTS;
};

// Lấy role hiện tại từ localStorage
export const getCurrentUserRole = () => {
  try {
    const userRaw = JSON.parse(localStorage.getItem("user") || "{}");
    // Nếu userRaw có field 'user', lấy role từ đó
    const role = userRaw.user?.role || userRaw.role;
    return role?.toLowerCase() || "staff";
  } catch {
    return "staff";
  }
};

// Lấy endpoints cho user hiện tại
export const getCurrentUserEndpoints = () => {
  const role = getCurrentUserRole();
  return getEndpointsByRole(role);
};

// ========== STATISTICS ENDPOINTS ==========
export const STATISTICS_ENDPOINTS = {
  DEPARTMENT_STATS: `${API_URL}/api/stats/departments`,
  USER_STATS: `${API_URL}/api/stats/users`,
  PROJECTS_BY_DEPARTMENT: `${API_URL}/api/stats/projects-by-department`,
  ALL_DEPARTMENTS_PROJECTS: `${API_URL}/api/stats/all-departments-projects`,
  STAFF_TASK_ASSIGNMENTS: `${API_URL}/api/stats/staff-task-assignments`,
  STAFF_DASHBOARD: `${API_URL}/api/stats/staff-dashboard`,
};

// Export API_URL để sử dụng ở các file khác
export { API_URL };