import React, { useState, useEffect, useRef, useCallback } from 'react';
import DocsIcon from '../../assets/docs.svg';
import PdfIcon from '../../assets/pdf.svg';
import ImageIcon from '../../assets/image.svg';
import MoreIcon from '../../assets/detail.svg';
import DownloadIcon from '../../assets/download.svg';
import DeleteIcon from '../../assets/trash.svg';
import loadFileIcon from "../../assets/loadfile.svg";
import fileTextIcon from "../../assets/file-text.svg";
import completeIcon from "../../assets/complete.svg";
import '../../styles/UpdateNote.css';
import closeLoc from '../../assets/closeLoc.svg';
import { validateNoteForm } from '../../utils/validation';
import { showToast } from '../../utils/toastUtils';
import { PERSONAL_NOTES_ENDPOINTS } from '../../api/endpoints';
import ConfirmDeletePopup from './ConfirmDeletePopup';

const UpdateNote = ({ note, onClose, onUpdate, onDelete, onRefresh }) => {
  const [showFileMenu, setShowFileMenu] = useState(null); // index of file menu open
  const [updatedNote, setUpdatedNote] = useState({
    title: '',
    content: ''
  });
  const [errors, setErrors] = useState({ title: '', content: '' });
  const modalRef = useRef(null);
  const [updating, setUpdating] = useState(false); // For file operations
  const [updatingNote, setUpdatingNote] = useState(false); // For note content update
  const [currentNote, setCurrentNote] = useState({
    ...note,
    files: note?.files || note?.attachments || [],
    attachments: note?.files || note?.attachments || []
  });
  const [pendingFiles, setPendingFiles] = useState([]);
  const [fileOperationState, setFileOperationState] = useState({}); // Track individual file operations
  const [deleteFilePopup, setDeleteFilePopup] = useState({ isOpen: false, fileId: null, fileName: '' });

  // Hàm để lưu trạng thái downloaded files vào localStorage
  const saveDownloadedFiles = useCallback((noteId, files) => {
    if (!noteId) return;
    
    const downloadedFiles = files
      .filter(f => f.isDownloaded)
      .map(f => ({
        fileId: f._id || f.id,
        fileName: f.name,
        downloadedAt: new Date().toISOString()
      }));
    
    localStorage.setItem(`downloaded_files_${noteId}`, JSON.stringify(downloadedFiles));
  }, []);

  // Hàm để khôi phục trạng thái downloaded files từ localStorage
  const loadDownloadedFiles = useCallback((noteId) => {
    if (!noteId) return [];
    
    try {
      const saved = localStorage.getItem(`downloaded_files_${noteId}`);
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error('Error loading downloaded files:', error);
      return [];
    }
  }, []);

  useEffect(() => {
    if (note) {
      setUpdatedNote({
        title: note.title,
        content: note.content
      });
      
      // Khôi phục trạng thái downloaded files
      const noteId = note.id || note._id;
      const downloadedFiles = loadDownloadedFiles(noteId);
      
      const filesWithDownloadedState = (note?.files || note?.attachments || []).map(file => {
        const isDownloaded = downloadedFiles.some(df => df.fileId === (file._id || file.id));
        return {
          ...file,
          isDownloaded: isDownloaded
        };
      });
      
      setCurrentNote({
        ...note,
        files: filesWithDownloadedState,
        attachments: filesWithDownloadedState
      });
      setShowFileMenu(null);
    }
  }, [note, loadDownloadedFiles]);

  const validate = () => {
    const validationErrors = validateNoteForm({ 
      title: updatedNote.title, 
      content: updatedNote.content 
    });
    setErrors(validationErrors);
    return !validationErrors.title && !validationErrors.content;
  };

  // Hàm để chọn icon dựa trên loại file
  const getFileIcon = (fileName) => {
    const ext = (fileName || '').split('.').pop().toLowerCase();
    if (ext === 'pdf') return PdfIcon;
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) return ImageIcon;
    if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'].includes(ext)) return DocsIcon;
    return DocsIcon; // Mặc định cho các loại khác
  };

  // Hàm để cắt ngắn tên file khoảng 12 ký tự
  const truncateFileName = (fileName) => {
    if (!fileName) return 'Tệp đính kèm';
    
    // Nếu tên file dài hơn 12 ký tự
    if (fileName.length > 12) {
      const extension = fileName.split('.').pop();
      const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
      return `${nameWithoutExt.substring(0, 8)}...${extension}`;
    }
    
    return fileName;
  };

  const handleUpdate = async () => {
    if (!validate()) return;
    if (updatingNote) return; // Prevent double submission
    
    setUpdatingNote(true);
    try {
      await onUpdate(updatedNote);
      
      // Nếu có file mới, upload lên
      if (pendingFiles.length > 0) {
        await handleUploadFiles(pendingFiles);
        setPendingFiles([]);
      }
      
      await fetchNoteDetail(currentNote.id || currentNote._id);
      
      // Refresh parent component
      if (onRefresh) {
        await onRefresh();
      }
      
      showToast('Cập nhật ghi chú thành công!', 'success');
      onClose();
    } catch (error) {
      console.error('Lỗi cập nhật ghi chú:', error);
      showToast('Có lỗi khi cập nhật ghi chú!', 'error');
    } finally {
      setUpdatingNote(false);
    }
  };

  const handleDelete = () => {
    onDelete(note.id);
    onClose();
  };

  const handleAddFile = () => {
    fileInputRef.current?.click();
  };

   const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setPendingFiles(prev => [...prev, ...files]);
    }
    e.target.value = "";
  };

  // Upload file cho ghi chú đã có
  const handleUploadFiles = async (files) => {
    if (!currentNote?.id && !currentNote?._id) return;
    const noteId = currentNote.id || currentNote._id;
    const form = new FormData();
    Array.from(files).forEach(file => form.append('files', file));
    const token = localStorage.getItem('token');
    setUpdating(true);
    
    console.log('handleUploadFiles - Starting upload for noteId:', noteId);
    console.log('handleUploadFiles - Current files before upload:', currentNote.files);
    
    try {
      const res = await fetch(PERSONAL_NOTES_ENDPOINTS.UPLOAD_FILES(noteId), {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` },
        body: form,
        signal: AbortSignal.timeout(60000),
      });
      const result = await res.json().catch(() => ({}));
      if (res.ok && result.success) {
        showToast('Tải tệp thành công!', 'success');
        console.log('handleUploadFiles - Upload successful, fetching note detail...');
        // Chỉ fetch lại chi tiết note, không gọi onRefresh ngay lập tức
        await fetchNoteDetail(noteId);
        console.log('handleUploadFiles - Note detail fetched');
        // Chỉ refresh parent component nếu cần thiết
        // if (onRefresh) {
        //   await onRefresh();
        // }
      } else {
        showToast(result.message || 'Tải tệp thất bại!', 'error');
      }
    } catch (err) {
      showToast('Có lỗi khi tải tệp!', 'error');
    } finally {
      setUpdating(false);
    }
  };

  // Download file đính kèm - Tải trực tiếp
  const handleDownloadFile = useCallback(async (fileId) => {
    if (!currentNote?.id && !currentNote?._id) {
      showToast('Không tìm thấy thông tin ghi chú!', 'error');
      return;
    }
    
    const noteId = currentNote.id || currentNote._id;
    const token = localStorage.getItem('token');
    
    if (!token) {
      showToast('Phiên đăng nhập đã hết hạn!', 'error');
      return;
    }
    
    // Tìm thông tin file
    const file = (currentNote.files || currentNote.attachments || []).find(f => 
      (f._id || f.id) === fileId
    );
    const fileName = file?.name || 'file';
    
    console.log('handleDownloadFile called for fileId:', fileId);
    console.log('Found file:', file);
    console.log('All files in currentNote:', currentNote.files);
    console.log('All attachments in currentNote:', currentNote.attachments);
    
    // Set loading state for this specific file
    setFileOperationState(prev => ({ ...prev, [`download_${fileId}`]: true }));
    
    try {
      const url = PERSONAL_NOTES_ENDPOINTS.DOWNLOAD_FILE(noteId, fileId);
      console.log('Download URL:', url);
      
      // Gọi API để lấy thông tin download
      const res = await fetch(url, {
        method: 'GET',
        headers: { 
          'Authorization': `Bearer ${token}`,
        },
        signal: AbortSignal.timeout(30000), // 30 giây timeout
      });
      
      console.log('Response status:', res.status);
      
      if (res.ok) {
        const result = await res.json();
        console.log('Download response:', result);
        
        if (result.success && result.data?.downloadUrl) {
          // Backend trả về signed URL, tải file trực tiếp từ URL đó
          const downloadUrl = result.data.downloadUrl;
          console.log('Download URL from response:', downloadUrl);
          console.log('Starting download for file:', fileName);
          
          // Tải file trực tiếp từ signed URL
          const downloadRes = await fetch(downloadUrl);
          console.log('Download response status:', downloadRes.status);
          
          if (downloadRes.ok) {
            const blob = await downloadRes.blob();
            console.log('Blob size:', blob.size);
            
            // Tạo URL cho blob
            const blobUrl = window.URL.createObjectURL(blob);
            
            // Tạo link tải file và click tự động
            const downloadLink = document.createElement('a');
            downloadLink.style.display = 'none';
            downloadLink.href = blobUrl;
            downloadLink.download = fileName;
            
            // Thêm vào DOM và click
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            
            // Giải phóng blob URL
            window.URL.revokeObjectURL(blobUrl);
            
            // Đánh dấu file đã được tải và lưu blob URL mới
            const newBlobUrl = window.URL.createObjectURL(blob);
            console.log('File downloaded successfully:', fileName);
            console.log('Setting isDownloaded = true for fileId:', fileId);
            console.log('Current currentNote before update:', currentNote);
            
            setCurrentNote(prev => {
              console.log('Previous state:', prev);
              
              const updatedFiles = (prev.files || []).map(f => {
                if ((f._id || f.id) === fileId) {
                  console.log('Updating file:', f.name, 'with isDownloaded = true');
                  return { ...f, isDownloaded: true, downloadedBlobUrl: newBlobUrl };
                }
                return f;
              });
              
              const updatedAttachments = (prev.attachments || []).map(f => {
                if ((f._id || f.id) === fileId) {
                  console.log('Updating attachment:', f.name, 'with isDownloaded = true');
                  return { ...f, isDownloaded: true, downloadedBlobUrl: newBlobUrl };
                }
                return f;
              });
              
              const newState = {
                ...prev,
                files: updatedFiles,
                attachments: updatedAttachments
              };
              
              console.log('New state after update:', newState);
              
              // Lưu trạng thái downloaded files vào localStorage
              const noteId = currentNote.id || currentNote._id;
              saveDownloadedFiles(noteId, newState.files);
              
              return newState;
            });
            
          } else {
            console.error('Download failed with status:', downloadRes.status);
            showToast('Không thể tải file từ URL!', 'error');
          }
        } else {
          showToast(result.message || 'Không thể tải file!', 'error');
        }
      } else {
        const errorResult = await res.json().catch(() => ({}));
        console.error('Error response:', errorResult);
        
        const errorMessage = errorResult.message || 
          (res.status === 404 ? 'File không tồn tại!' : 
           res.status === 403 ? 'Không có quyền tải file này!' :
           res.status === 401 ? 'Phiên đăng nhập đã hết hạn!' :
           'Tải file thất bại!');
        showToast(errorMessage, 'error');
      }
    } catch (err) {
      console.error('Lỗi khi tải file:', err);
      if (err.name === 'AbortError') {
        showToast('Yêu cầu tải file đã bị hủy!', 'error');
      } else if (err.name === 'TypeError' && err.message.includes('fetch')) {
        showToast('Không thể kết nối đến máy chủ!', 'error');
      } else {
        showToast('Có lỗi khi tải file!', 'error');
      }
    } finally {
      setFileOperationState(prev => ({ ...prev, [`download_${fileId}`]: false }));
    }
  }, [currentNote, saveDownloadedFiles]);

  // Xóa file đính kèm - Hoàn thiện
  const handleDeleteFile = useCallback(async (fileId) => {
    if (!currentNote?.id && !currentNote?._id) {
      showToast('Không tìm thấy thông tin ghi chú!', 'error');
      return;
    }
    
    const noteId = currentNote.id || currentNote._id;
    const token = localStorage.getItem('token');
    
    if (!token) {
      showToast('Phiên đăng nhập đã hết hạn!', 'error');
      return;
    }
    
    // Tìm thông tin file
    const file = (currentNote.files || currentNote.attachments || []).find(f => 
      (f._id || f.id) === fileId
    );
    const fileName = file?.name || 'file';
    
    // Set loading state for this specific file
    setFileOperationState(prev => ({ ...prev, [`delete_${fileId}`]: true }));
    
    try {
      const url = PERSONAL_NOTES_ENDPOINTS.DELETE_FILE(noteId, fileId);
      
      const res = await fetch(url, {
        method: 'DELETE',
        headers: { 
          'Authorization': `Bearer ${token}`,
        },
        signal: AbortSignal.timeout(30000), // 30 giây timeout
      });
      
      const result = await res.json().catch(() => ({}));
      
      if (res.ok && result.success) {
        showToast(`Đã xóa file "${fileName}" thành công!`, 'success');
        
        // Refresh data
        await fetchNoteDetail(noteId);
        
        // Cập nhật trạng thái downloaded files trong localStorage
        const updatedFiles = (currentNote.files || []).filter(f => (f._id || f.id) !== fileId);
        saveDownloadedFiles(noteId, updatedFiles);
        
        if (onRefresh) {
          await onRefresh();
        }
      } else {
        const errorMessage = result.message || 
          (res.status === 404 ? 'File không tồn tại!' : 
           res.status === 403 ? 'Không có quyền xóa file này!' :
           res.status === 401 ? 'Phiên đăng nhập đã hết hạn!' :
           'Xóa file thất bại!');
        showToast(errorMessage, 'error');
      }
    } catch (err) {
      console.error('Lỗi khi xóa file:', err);
      if (err.name === 'AbortError') {
        showToast('Yêu cầu xóa file đã bị hủy!', 'error');
      } else if (err.name === 'TypeError' && err.message.includes('fetch')) {
        showToast('Không thể kết nối đến máy chủ!', 'error');
      } else {
        showToast('Có lỗi khi xóa file!', 'error');
      }
    } finally {
      setFileOperationState(prev => ({ ...prev, [`delete_${fileId}`]: false }));
    }
  }, [currentNote, saveDownloadedFiles, onRefresh]);

  // Mở popup xác nhận xóa file
  const openDeleteFilePopup = (fileId) => {
    const file = (currentNote.files || currentNote.attachments || []).find(f => 
      (f._id || f.id) === fileId
    );
    const fileName = file?.name || 'file';
    setDeleteFilePopup({ isOpen: true, fileId: fileId, fileName: fileName });
  };

  // Fetch lại chi tiết ghi chú
  const fetchNoteDetail = async (noteId) => {
    const token = localStorage.getItem('token');
    try {
      // Lấy thông tin ghi chú và file cùng lúc
      const [noteRes, filesRes] = await Promise.all([
        fetch(`${PERSONAL_NOTES_ENDPOINTS.GET_NOTES}/${noteId}`, {
          headers: { 'Authorization': `Bearer ${token}` },
          signal: AbortSignal.timeout(30000),
        }),
        fetch(PERSONAL_NOTES_ENDPOINTS.GET_FILES(noteId), {
          headers: { 'Authorization': `Bearer ${token}` },
          signal: AbortSignal.timeout(30000),
        })
      ]);
      
      let noteData = null;
      let filesData = null;
      
      if (noteRes.ok) {
        const response = await noteRes.json();
        if (response.success && response.data) {
          noteData = response.data;
        }
      }
      
      if (filesRes.ok) {
        const filesResponse = await filesRes.json();
        if (filesResponse.success && filesResponse.data?.files) {
          filesData = filesResponse.data.files;
        }
      }
      
      // Cập nhật state một lần duy nhất với cả thông tin note và files
      setCurrentNote(prev => {
        console.log('fetchNoteDetail - Previous state:', prev);
        console.log('fetchNoteDetail - Previous files:', prev.files);
        
        const currentFiles = prev.files || [];
        const newFiles = (filesData || []).map(newFile => {
          const existingFile = currentFiles.find(f => (f._id || f.id) === (newFile._id || newFile.id));
          console.log('fetchNoteDetail - Processing file:', newFile.name, 'existingFile:', existingFile);
          
          // Giữ nguyên trạng thái isDownloaded và downloadedBlobUrl nếu file đã tồn tại
          if (existingFile) {
            console.log('fetchNoteDetail - Keeping download status for:', newFile.name, 'isDownloaded:', existingFile.isDownloaded);
            return {
              ...newFile,
              isDownloaded: existingFile.isDownloaded || false,
              downloadedBlobUrl: existingFile.downloadedBlobUrl || null
            };
          }
          // File mới, không có trạng thái download
          console.log('fetchNoteDetail - New file:', newFile.name, 'setting isDownloaded: false');
          return {
            ...newFile,
            isDownloaded: false,
            downloadedBlobUrl: null
          };
        });
        
        console.log('fetchNoteDetail - New files:', newFiles);
        
        const newState = {
          id: noteData?._id || noteData?.id || prev.id,
          title: noteData?.title || prev.title,
          content: noteData?.description || prev.content,
          date: noteData?.date || noteData?.createdAt || prev.date,
          files: newFiles,
          attachments: newFiles
        };
        
        console.log('fetchNoteDetail - New state:', newState);
        
        return newState;
      });
      
    } catch (error) {
      console.error('Error fetching note detail:', error);
    }
  };

  const [formData, setFormData] = useState({
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      priority: "medium",
      members: [],
      attachments: [],
    });

    const fileInputRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      console.log('handleClickOutside called, deleteFilePopup.isOpen:', deleteFilePopup.isOpen);
      
      // Không đóng form chính nếu popup xác nhận đang mở
      if (deleteFilePopup.isOpen) {
        console.log('Popup is open, not closing main form');
        return;
      }
      
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        console.log('Closing main form');
        onClose && onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose, deleteFilePopup.isOpen]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showFileMenu !== null) {
        setShowFileMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFileMenu]);

  const fileList = [
    ...(currentNote.files || currentNote.attachments || []).map(file => ({
      ...file,
      id: file._id || file.id, // Ensure consistent ID field
      _id: file._id || file.id, // Keep both for compatibility
      isPending: false,
      isDownloaded: file.isDownloaded || false, // Giữ nguyên trạng thái isDownloaded
      downloadedBlobUrl: file.downloadedBlobUrl || null // Giữ nguyên downloadedBlobUrl
    })),
    ...pendingFiles.map(file => ({
      name: file.name,
      isPending: true,
      _fileObj: file,
    }))
  ];

  // Debug: Log currentNote và fileList
  console.log('currentNote:', currentNote);
  console.log('fileList:', fileList);

  // Debug: Theo dõi khi currentNote thay đổi
  useEffect(() => {
    console.log('currentNote changed:', currentNote);
    if (currentNote?.files) {
      console.log('Files in currentNote:', (currentNote.files || []).map(f => ({
        name: f.name,
        isDownloaded: f.isDownloaded,
        id: f._id || f.id
      })));
    }
  }, [currentNote]);

  // Thêm các hàm xử lý click riêng biệt
  const handleDownloadClick = (fileId) => {
    setShowFileMenu(null);
    handleDownloadFile(fileId);
  };

  const handleDeleteClick = (fileId) => {
    setShowFileMenu(null);
    handleDeleteFile(fileId);
  };

  const handleMenuClick = (idx) => {
    setShowFileMenu(showFileMenu === idx ? null : idx);
  };

  const handleViewFile = async (fileId, isDownloadedView = false) => {
    if (!currentNote?.id && !currentNote?._id) {
      showToast('Không tìm thấy thông tin ghi chú!', 'error');
      return;
    }
    
    // Tìm thông tin file
    const file = (currentNote.files || currentNote.attachments || []).find(f => 
      (f._id || f.id) === fileId
    );
    
    if (!file) {
      showToast('Không tìm thấy file!', 'error');
      return;
    }
    
    // Chỉ kiểm tra isDownloaded khi bấm nút "Xem file đã tải"
    if (isDownloadedView && !file.isDownloaded) {
      showToast('Vui lòng tải file trước khi xem!', 'error');
      return;
    }
    
    const fileName = file.name || 'file';
    
    // Set loading state for this specific file
    setFileOperationState(prev => ({ ...prev, [`view_${fileId}`]: true }));
    
    try {
      // Nếu là "Xem file đã tải", sử dụng file đã tải về từ blob URL
      if (isDownloadedView && file.downloadedBlobUrl) {
        const fileExtension = fileName.split('.').pop().toLowerCase();
        let viewUrl = file.downloadedBlobUrl;
        
        // Nếu là file Word, Excel, PowerPoint, sử dụng Google Docs Viewer
        if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension)) {
          viewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(file.downloadedBlobUrl)}&embedded=true`;
        }
        // Nếu là file PDF, mở trực tiếp
        else if (fileExtension === 'pdf') {
          viewUrl = file.downloadedBlobUrl; // Mở trực tiếp PDF
        }
        // Các file khác mở trực tiếp
        else {
          viewUrl = file.downloadedBlobUrl;
        }
        
        // Mở file trong tab mới
        const newWindow = window.open(viewUrl, '_blank');
        
        if (newWindow) {
          // showToast(`Đang mở file "${fileName}" từ máy tính...`, 'success');
        } else {
          showToast('Trình duyệt đã chặn popup. Vui lòng cho phép popup và thử lại!', 'error');
        }
        return;
      }
      
      // Thử sử dụng downloadUrl trực tiếp nếu có (cho xem trước)
      if (file.downloadUrl) {
        const fileUrl = file.downloadUrl;
        const fileExtension = fileName.split('.').pop().toLowerCase();
        let viewUrl = fileUrl;
        
        // Nếu là file Word, Excel, PowerPoint, sử dụng Google Docs Viewer
        if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension)) {
          viewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`;
        }
        // Nếu là file PDF, mở trực tiếp
        else if (fileExtension === 'pdf') {
          viewUrl = fileUrl; // Mở trực tiếp PDF
        }
        // Các file khác mở trực tiếp
        else {
          viewUrl = fileUrl;
        }
        
        // Mở file trong tab mới
        const newWindow = window.open(viewUrl, '_blank');
        
        if (newWindow) {
          // showToast(`Đang mở file "${fileName}"...`, 'success');
        } else {
          showToast('Trình duyệt đã chặn popup. Vui lòng cho phép popup và thử lại!', 'error');
        }
        return;
      }
      
      // Nếu không có downloadUrl, lấy signed URL từ backend
      const noteId = currentNote.id || currentNote._id;
      const token = localStorage.getItem('token');
      
      if (!token) {
        showToast('Phiên đăng nhập đã hết hạn!', 'error');
        return;
      }
      
      const url = PERSONAL_NOTES_ENDPOINTS.DOWNLOAD_FILE(noteId, fileId);
      const res = await fetch(url, {
        method: 'GET',
        headers: { 
          'Authorization': `Bearer ${token}`,
        },
        signal: AbortSignal.timeout(30000), // 30 giây timeout
      });
      
      if (res.ok) {
        const result = await res.json();
        
        if (result.success && result.data?.downloadUrl) {
          const fileUrl = result.data.downloadUrl;
          
          // Xác định loại file để quyết định cách mở
          const fileExtension = fileName.split('.').pop().toLowerCase();
          let viewUrl = fileUrl;
          
          // Nếu là file Word, Excel, PowerPoint, sử dụng Google Docs Viewer
          if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension)) {
            viewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`;
          }
          // Nếu là file PDF, mở trực tiếp
          else if (fileExtension === 'pdf') {
            viewUrl = fileUrl; // Mở trực tiếp PDF
          }
          // Các file khác mở trực tiếp
          else {
            viewUrl = fileUrl;
          }
          
          // Mở file trong tab mới
          const newWindow = window.open(viewUrl, '_blank');
          
          if (newWindow) {
            // showToast(`Đang mở file "${fileName}"...`, 'success');
          } else {
            showToast('Trình duyệt đã chặn popup. Vui lòng cho phép popup và thử lại!', 'error');
          }
        } else {
          showToast(result.message || 'Không thể xem file!', 'error');
        }
      } else {
        const errorResult = await res.json().catch(() => ({}));
        const errorMessage = errorResult.message || 
          (res.status === 404 ? 'File không tồn tại!' : 
           res.status === 403 ? 'Không có quyền xem file này!' :
           res.status === 401 ? 'Phiên đăng nhập đã hết hạn!' :
           'Xem file thất bại!');
        showToast(errorMessage, 'error');
      }
      
    } catch (err) {
      console.error('Lỗi khi xem file:', err);
      if (err.name === 'AbortError') {
        showToast('Yêu cầu xem file đã bị hủy!', 'error');
      } else if (err.name === 'TypeError' && err.message.includes('fetch')) {
        showToast('Không thể kết nối đến máy chủ!', 'error');
      } else {
        showToast('Có lỗi khi xem file!', 'error');
      }
    } finally {
      setFileOperationState(prev => ({ ...prev, [`view_${fileId}`]: false }));
    }
  };

  return (
    <div className="update-note-modal">
      <form className="update-note-form" ref={modalRef} onSubmit={e => { e.preventDefault(); handleUpdate(); }}>
        <div className="update-note-header">
          <h2 className="update-note-title">Chỉnh sửa ghi chú</h2>
          <button type="button" className="update-note-close" onClick={onClose}>
            <img src={closeLoc} alt="Close" />
          </button>
        </div>
        
        <div className="update-note-input-container">
          <input
            type="text"
            value={updatedNote.title}
            onChange={e => setUpdatedNote({ ...updatedNote, title: e.target.value })}
            placeholder=""
            className={`update-note-input ${errors.title ? 'update-note-input-error' : ''}`}
          />
          {errors.title && <div className="update-note-error-message">{errors.title}</div>}
        </div>
        <div className="update-note-textarea-container">
          <textarea
            value={updatedNote.content}
            onChange={e => setUpdatedNote({ ...updatedNote, content: e.target.value })}
            placeholder=""
            rows={5}
            className={`update-note-textarea ${errors.content ? 'update-note-input-error' : ''}`}
          />
          {errors.content && <div className="update-note-error-message">{errors.content}</div>}
        {/* Hiển thị danh sách tệp đính kèm */}
        <div className="update-note-files">
          <div className="update-note-files-label">Tệp tài liệu</div>
          <div className="job-panel-rows">
                    <div className="job-panel-value" style={{flexDirection: 'column', alignItems: 'flex-start', gap: 0}}>
                      <div className="job-panel-file-upload-custom" onClick={handleAddFile}>
                        <input
                          type="file"
                          multiple
                          accept=".pdf,.doc,.docx"
                          onChange={handleFileUpload}
                          style={{ display: "none" }}
                          ref={fileInputRef}
                        />
                        <img src={loadFileIcon} alt="Tải tệp" className="job-panel-file-upload-icon" />
                        <span className="job-panel-file-upload-text">Bấm vào để tải lên tệp</span>
                      </div>
                    </div>
                  </div>
        </div>
        
          {fileList.length > 0 ? (
            fileList.map((file, idx) => {
              const icon = getFileIcon(file.name);
              console.log('File in list:', file.name, 'isDownloaded:', file.isDownloaded);
              return (
                <div key={idx} className="update-note-file-row" style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  background: file.isPending ? '#fffbe6' : '#f9eaea', 
                  borderRadius: 8, 
                  marginBottom: 8, 
                  padding: '6px 12px', 
                  position: 'relative',
                  opacity: (fileOperationState[`download_${file._id || file.id}`] || fileOperationState[`delete_${file._id || file.id}`] || fileOperationState[`view_${file._id || file.id}`]) ? 0.7 : 1,
                  cursor: 'default'
                }}
                >
                  <img src={icon} alt="file icon" style={{ width: 20, height: 20, marginRight: 8 }} />
                  <span style={{ fontSize: 15, flex: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                    {truncateFileName(file.name)}
                    {(fileOperationState[`download_${file._id || file.id}`] || fileOperationState[`delete_${file._id || file.id}`] || fileOperationState[`view_${file._id || file.id}`]) && (
                      <span style={{ marginLeft: 8, fontSize: 12, color: '#666' }}>
                        ({fileOperationState[`download_${file._id || file.id}`] ? 'Đang tải...' : fileOperationState[`delete_${file._id || file.id}`] ? 'Đang xóa...' : fileOperationState[`view_${file._id || file.id}`] ? 'Đang xem...' : ''})
                      </span>
                    )}
                  </span>
                  
                  {!file.isPending && (
                    <div style={{ display: 'flex', alignItems: 'center', gap: 156 }}>
                      {/* Nút xem file đã tải - chỉ hiển thị cho file đã tải */}
                      {file.isDownloaded && (
                      <button 
                        type="button" 
                        style={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          background: 'none', 
                          border: 'none', 
                          cursor: 'pointer', 
                          color: '#4caf50',
                          fontSize: 14,
                          padding: '4px 8px',
                          borderRadius: 4
                        }} 
                        onClick={(e) => {
                          e.stopPropagation();
                            handleViewFile(file._id || file.id, true);
                        }}
                          title="Xem file đã tải trong trình duyệt"
                      >
                        <div style={{ 
                          width: 16, 
                          height: 16, 
                          marginRight: 6, 
                          borderRadius: '50%', 
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <img src={completeIcon} alt="Complete" style={{ width: 18, height: 18 }} />
                        </div>
                          Xem file đã tải
                      </button>
                      )}
                      
                      {/* Nút menu 3 chấm */}
                      <button 
                        type="button" 
                        className="update-note-file-menu-btn" 
                        style={{ 
                          background: 'none', 
                          border: 'none', 
                          cursor: 'pointer',
                          padding: 4
                        }} 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMenuClick(idx);
                        }}
                      >
                        <img src={MoreIcon} alt="menu" style={{ width: 22, height: 22 }} />
                      </button>
                    </div>
                  )}
                  
                  {file.isPending && (
                    <button
                      type="button"
                      className="update-note-file-cancel-btn"
                      style={{ background: 'none', border: 'none', cursor: 'pointer', color: '#e53935', padding: 4 }}
                      title="Huỷ file chờ"
                      onClick={(e) => {
                        e.stopPropagation();
                        setPendingFiles(pendingFiles.filter(f => f.name !== file.name));
                      }}
                    >
                      Hủy
                    </button>
                  )}
                  
                  {/* Menu dropdown */}
                  {showFileMenu === idx && !file.isPending && (
                    <div className="update-note-file-menu" style={{ position: 'absolute', top: 36, right: 0, background: '#fff', boxShadow: '0 2px 8px rgba(0,0,0,0.12)', borderRadius: 8, zIndex: 10, minWidth: 140 }}>
                      <div style={{ padding: '8px 12px', fontWeight: 500, fontSize: 13, color: '#888' }}>Hành động</div>
                      
                      {/* Nút tải file */}
                      <button 
                        type="button" 
                        style={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          width: '100%', 
                          background: 'none', 
                          border: 'none', 
                          padding: '8px 12px', 
                          cursor: 'pointer', 
                          fontSize: 14,
                          borderBottom: '1px solid #eee'
                        }} 
                        onMouseDown={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setShowFileMenu(null);
                          handleDownloadFile(file._id || file.id);
                        }}
                        title="Tải file về máy"
                      >
                        <img src={DownloadIcon} alt="download" style={{ width: 18, height: 18, marginRight: 8 }} /> 
                        Tải file
                      </button>
                      
                      {/* Nút xóa file */}
                      <button 
                        type="button" 
                        style={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          width: '100%', 
                          background: 'none', 
                          border: 'none', 
                          padding: '8px 12px', 
                          cursor: 'pointer', 
                          color: '#e53935', 
                          fontSize: 14
                        }} 
                        onMouseDown={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setShowFileMenu(null);
                          openDeleteFilePopup(file._id || file.id);
                        }}
                      >
                        <img src={DeleteIcon} alt="delete" style={{ width: 18, height: 18, marginRight: 8 }} /> 
                        Xoá file
                      </button>
                    </div>
                  )}
                </div>
              );
            })
          ) : (
            <div style={{ color: '#888', fontSize: 15, padding: '8px 0 8px 8px' }}>Không có tài liệu</div>
          )}
        </div>
        
        <div className="update-note-buttons">
          <button type="button" className="update-note-delete" onClick={handleDelete} disabled={updatingNote}>
            Xoá ghi chú
          </button>
          <button type="submit" className="update-note-submit" disabled={updatingNote}>
            {updatingNote ? 'Đang cập nhật...' : 'Cập nhật ghi chú'}
          </button>
        </div>
      </form>
      <ConfirmDeletePopup
        isOpen={deleteFilePopup.isOpen}
        onCancel={() => {
          console.log('Cancel clicked - only closing popup');
          setDeleteFilePopup({ isOpen: false, fileId: null, fileName: '' });
        }}
        onConfirm={() => {
          console.log('Confirm clicked - deleting file and closing popup');
          handleDeleteFile(deleteFilePopup.fileId);
          setDeleteFilePopup({ isOpen: false, fileId: null, fileName: '' });
        }}
        fileName={deleteFilePopup.fileName}
      />
    </div>
  );
};

export default UpdateNote;
